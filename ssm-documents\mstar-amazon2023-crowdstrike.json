﻿{
  "schemaVersion": "2.2",
  "description": "Install Crowdstrike Amazon linux 2023",
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "Amazon2023",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "runCommand": [
          "#!/bin/bash",
          "# URL for the Instance Identity Document",
          "instanceIdentityUrl='http://***************/latest/dynamic/instance-identity/document'",
          "# Fetch the Instance Identity Document",
          "aws_token=$(curl -X PUT 'http://***************/latest/api/token' -H 'X-aws-ec2-metadata-token-ttl-seconds: 180')",
          "identity_document=$(curl -H \"X-aws-ec2-metadata-token: $aws_token\" \"$instanceIdentityUrl\")",
          "# Extract the Account ID",
          "accountId=$(echo $identity_document | grep -o '\"accountId\" : \"[^\"]*\"' | awk -F'\"' '{print $4}')",
          " ## Installing Crowdstrike Sensor",
          "if sudo ls /opt/CrowdStrike /opt/CrowdStrike/falcon-sensor &> /dev/null && ps -e | grep -w 'falcon-sensor' | grep -v 'grep' &> /dev/null; then",
          "echo 'Crowdstrike Sensor is installed'",
          "else",
          "echo 'Crowdstrike Sensor is not installed'",
          "cmd=`uname -i | grep x86_64 > /dev/null 2>&1; echo $?` ; if [[ $cmd -eq 0 ]]; then sudo wget -O /tmp/crowdstrike_amazon2023.rpm https://mstar-prod-repos.s3.amazonaws.com/software/crowdstrike/crowdstrike_amazon2023.rpm; else sudo wget -O /tmp/crowdstrike_amazon2023.rpm https://mstar-prod-repos.s3.us-east-1.amazonaws.com/software/crowdstrike/crowdstrike_amazon2023_arm.rpm; fi",
          "sudo yum install -y /tmp/crowdstrike_amazon2023.rpm",
          "sudo /opt/CrowdStrike/falconctl -s --cid=07EFFBB0881743CF9F8896927FD2B397-C4",
          "sudo /opt/CrowdStrike/falconctl -s --tags=\"aws,server,$accountId\"",
          "sudo rm -f /tmp/crowdstrike_amazon2023.rpm*",
          "fi",
          "sudo systemctl start falcon-sensor"
        ]
      }
    }
  ]
}

