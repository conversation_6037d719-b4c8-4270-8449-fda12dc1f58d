﻿{
  "schemaVersion": "2.2",
  "description": "Amazon linux 2023 Bootstraping",
  "mainSteps": [
    {
      "action": "aws:runDocument",
      "name": "mstarbase",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-amazon2023-platform"
      }
    },
    {
      "action": "aws:runDocument",
      "name": "cisbenchmark",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-amazon2023-cis-benchmark"
      }
    },
    {
      "action": "aws:runDocument",
      "name": "global",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-amazon2023-global"
      }
    },
    {
      "action": "aws:runDocument",
      "name": "selinux",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-amazon2023-selinux"
      }
    },
    {
      "action": "aws:runDocument",
      "name": "splunk",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-amazon2023-splunk-forwarder"
      }
    },
     {
      "action": "aws:runDocument",
      "name": "crowdstrike",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-amazon2023-crowdstrike"
      }
    },
    {
      "action": "aws:runDocument",
      "name": "cloudwatch",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-amazon2023-cloudwatch"
      }
    },
    {
      "action": "aws:runDocument",
      "name": "domainjoin",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "documentType": "SSMDocument",
        "documentPath": "arn:aws:ssm:us-east-1:903581193685:document/mstar-domain-join-linux"
      }
    }
  ]
}

