﻿{
  "schemaVersion": "2.2",
  "description": "Amazon linux 2 hardening",
  "parameters": {
    "InitialWebText": {
      "type": "String",
      "description": "Initial message",
      "default": "Welcome to instance"
    }
  },
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "hardeningamazon2os",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "runCommand": [
          "cmd=`cat /etc/fstab| grep tmpfs > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo sed -i '$atmpfs /dev/shm tmpfs rw,nosuid,nodev,seclabel,noexec 0 0' /etc/fstab; fi",
          "sudo /bin/env mount -o remount tmpfs",
          "sudo /sbin/lsmod | grep freevxfs ; if [[ $? == 0 ]]; then sudo modprobe -rf freevxfs; fi",
          "sudo /sbin/lsmod | grep jffs2 ; if [[ $? == 0 ]]; then sudo modprobe -rf jffs2; fi",
          "sudo /sbin/lsmod | grep hfs ; if [[ $? == 0 ]]; then sudo modprobe -rf hfs; fi",
          "sudo /sbin/lsmod | grep hfsplus ; if [[ $? == 0 ]]; then sudo modprobe -rf hfsplus; fi",
          "sudo /sbin/lsmod | grep squashfs ; if [[ $? == 0 ]]; then sudo modprobe -rf squashfs; fi",
          "sudo /sbin/lsmod | grep udf ; if [[ $? == 0 ]]; then sudo modprobe -rf udf; fi",
          "sudo /sbin/lsmod | grep cramfs ; if [[ $? == 0 ]]; then sudo modprobe -rf cramfs; fi",
          "sudo cp /etc/security/limits.conf /etc/security/limits.conf.orig; sudo echo \"* hard core 0\" | sudo tee /etc/security/limits.conf > /dev/null",
          "cmd=`rpm -qa | grep docker  > /dev/null 2>&1; echo $?`; if [[ $cmd = 0 ]]; then sudo /sbin/sysctl -w net.ipv4.ip_forward=1; else sudo /sbin/sysctl -w net.ipv4.ip_forward=0; fi",
          "cmd=`sudo /sbin/sysctl -a | grep 'net.ipv6.conf.all.accept_ra = 0' > /dev/null 2>&1 ; echo $?`; if [[ $cmd -ge 1 ]]; then sudo /sbin/sysctl -w net.ipv6.conf.all.accept_ra=0; fi",
          "cmd=`sudo /sbin/sysctl -a | grep 'net.ipv6.conf.default.accept_ra = 0' > /dev/null 2>&1 ; echo $?`; if [[ $cmd -ge 1 ]]; then sudo /sbin/sysctl -w net.ipv6.conf.default.accept_ra=0; fi",
          "cmd=`sudo /sbin/sysctl -a | grep 'net.ipv6.conf.all.accept_redirects = 0' > /dev/null 2>&1 ; echo $?`; if [[ $cmd -ge 1 ]]; then sudo /sbin/sysctl -w net.ipv6.conf.all.accept_redirects=0; fi",
          "cmd=`sudo /sbin/sysctl -a | grep 'net.ipv6.conf.default.disable_ipv6 = 1' > /dev/null 2>&1 ; echo $?`; if [[ $cmd -ge 1 ]]; then sudo /sbin/sysctl -w net.ipv6.conf.default.disable_ipv6=1; fi",
	  "sudo /sbin/sysctl -w net.ipv4.conf.default.accept_source_route=0",
	  "sudo /sbin/sysctl -w net.ipv6.conf.default.accept_redirects=0",
          "cmd=`sysctl -a |grep 'net.ipv4.tcp_syncookies = 0'`; if [[ $? -ne 0 ]]; then echo 'net.ipv4.tcp_syncookies=0' >> /etc/sysctl.d/cis_sysctl.conf && sysctl -p /etc/sysctl.d/cis_sysctl.conf; fi",
          "cmd=`sysctl -a |grep 'net.ipv4.conf.all.accept_source_route = 0'`; if [[ $? -ne 0 ]]; then echo 'net.ipv4.conf.all.accept_source_route=0' >> /etc/sysctl.d/cis_sysctl.conf && sysctl -p /etc/sysctl.d/cis_sysctl.conf; fi",
          "sudo cp /tmp/s3_files/rsyslog.conf-rhel9.txt /etc/rsyslog.conf",
          "sudo systemctl restart rsyslog.service; sudo systemctl enable rsyslog.service",
          "sudo cp /tmp/s3_files/sysconfig_auditd-rhel6.txt /etc/sysconfig/auditd",
          "sudo cp /tmp/s3_files/audit.rules-default-rhel7 /etc/audit/rules.d/audit.rules",
          "sudo cp /tmp/s3_files/audit-infosec.rules /etc/audit/rules.d/audit-infosec.rules",
          "sudo systemctl kill auditd; sudo systemctl start auditd; sudo systemctl enable auditd.service",
          "sudo cp /tmp/s3_files/audit.rules-cis-rhel6 /etc/audit/rules.d/cis.rules",
          "sudo cp /tmp/s3_files/audit.rules-suids-rhel6 /etc/audit/rules.d/suids.rules",
          "sudo cp /tmp/s3_files/logrotate_syslog-rhel6.txt /etc/logrotate.d/syslog",
          "sudo touch /etc/at.allow; sudo chmod 600 /etc/at.allow",
          "sudo rm -f /etc/at.deny",
          "sudo touch /etc/cron.allow; sudo chmod 600 /etc/cron.allow",
          "sudo rm -f /etc/cron.deny",
          "sudo /usr/sbin/authconfig --test | grep 'password hashing algorithm is sha512'",
          "sudo yum install libpwquality -y",
          "sudo cp /tmp/s3_files/pam_pwquality-rhel7.txt /etc/security/pwquality.conf",
          "sudo cp /tmp/s3_files/pam_password-auth-ac-rhel7.txt /etc/pam.d/password-auth-ac",
          "sudo cp /tmp/s3_files/pam_system-auth-ac-rhel6.txt /etc/pam.d/system-auth-ac",
          "sudo cp /tmp/s3_files/login.defs-rhel6 /etc/login.defs",
          "sudo echo -e 'umask 077' | sudo tee /etc/profile.d/cis.sh > /dev/null",
	  "PASSWORD=$(sudo openssl rand -base64 12) && sudo echo \"root:$PASSWORD\" | sudo chpasswd && sudo passwd -l root",
          "sudo chmod 644 /var/lib/update-motd/motd; sudo chmod 644 /etc/passwd; sudo chmod 000 /etc/shadow; sudo chmod 000 /etc/gshadow; sudo chmod 644 /etc/group",
          "cmd=`test -z \"$(find /home/<USER>/ -type f -perm -o+w -o -type d -perm -o+w)\" > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then find /home/<USER>/ -type f -perm -o+w -o -type d -perm -o+w | xargs chmod \"o-w\"; fi",
          "cmd=`test -z \"$(find /usr/local ! -user root)\" > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then find /usr/local ! -user root -exec chown -h root:root {} \\;; fi",
          "cmd=`cat /etc/rsyslog.conf |grep -E '/var/log/daemon.log'`; if [[ $? -ne 0 ]]; then echo 'Kindly configure rsyslog logging!!'; fi",
          "cmd=`cat /etc/rsyslog.conf |grep -E '/var/log/syslog.log'`; if [[ $? -ne 0 ]]; then echo 'kindly configure rsyslog logging!!'; fi",
          "cmd=`cat /etc/rsyslog.conf |grep -E '/var/log/messages'`; if [[ $? -ne 0 ]]; then echo 'kindly configure rsyslog logging!!'; fi",
          "cmd=`cat /etc/rsyslog.conf |grep -E '/var/log/secure'`; if [[ $? -ne 0 ]]; then echo 'kindly configure rsyslog logging!!'; fi",
          "cmd=`cat /etc/audit/auditd.conf | grep 'max_log_file = 1'`; if [[ $? -ne 0 ]]; then echo 'Configure max_log_file in /etc/audit/auditd.conf'; fi",
          "sudo chown root:root /etc/crontab /etc/cron.hourly /etc/cron.daily /etc/cron.weekly /etc/cron.monthly /etc/passwd /etc/group",
          "sudo chmod 700 /etc/cron.hourly /etc/cron.weekly /etc/cron.monthly /etc/cron.daily",
          "sudo chmod 600 /etc/crontab",
	  "sudo chmod 700 /etc/cron.daily; sudo chmod 700 /etc/cron.hourly; sudo chmod 700 /etc/cron.monthly; sudo chmod 700 /etc/cron.weekly",
          "cmd=`cat /etc/ssh/sshd_config | grep X11Forwarding`; if [[ ${cmd} == *\"yes\"* ]]; then echo \"Verify X11Forwarding in sshd_config configuration file\"; fi",
          "cmd=`cat /etc/ssh/sshd_config | grep PermitRootLogin`; if [[ ${cmd} != *\"no\"* ]]; then echo \"Verify PermitRootLogin in sshd_config configuration file\"; fi",
          "cmd=`cat /etc/ssh/sshd_config | grep MaxAuthTries | awk {'print $2'}`; if [[ $cmd > 5 ]]; then echo 'Decrease the MaxAuthTries below 4'; fi",
          "cmd=`cat /etc/ssh/sshd_config | grep ClientAliveInterval | awk {'print $2'}`; if [[ $cmd < 200 ]]; then echo 'Increase the ClientAliveInterval to 300'; fi",
          "cmd=`cat /etc/ssh/sshd_config | grep ClientAliveCountMax | awk {'print $2'}`; if [[ $cmd > 1 ]]; then echo 'Verify the ClientAliveCountMax in /etc/ssh/sshd_config file'; fi",
          "cmd=`cat /etc/ssh/sshd_config | grep banner | awk {'print $2'}`; if [[ ${cmd} != *'/etc/banner'* ]]; then echo 'Add /etc/banner to /etc/ssh/sshd_config file'; fi"
        ]
      }
    }
  ]
}

