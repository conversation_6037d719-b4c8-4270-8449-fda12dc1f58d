﻿{
  "schemaVersion": "2.2",
  "description": "Amazon linux 2 hardening",
  "parameters": {
    "InitialWebText": {
      "type": "String",
      "description": "Initial message",
      "default": "Welcome to instance"
    }
  },
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "hardeningamazon2os",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "runCommand": [
          "sudo sed -i 's/^SELINUX=permissive/SELINUX=enforcing/' /etc/selinux/config",
          "sudo setenforce 1",
          "cmd=`sudo ls /data > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo mkdir /data; fi",
          "sudo chown root:root /data;sudo chmod 0755 /data",
          "sudo /usr/bin/env restorecon -Rv /data",
          "sudo cp /tmp/s3_files/mstar.data-useradd.te.txt .",
          "sudo mv mstar.data-useradd.te.txt mstar.data-useradd.te",
          "sudo dos2unix mstar.data-useradd.te",
          "sudo /usr/bin/make -f /usr/share/selinux/devel/Makefile mstar.data-useradd.pp",
          "sudo /usr/sbin/semodule -i mstar.data-useradd.pp",
          "sudo cp /tmp/s3_files/mstar.logrotate-mailspool.te.txt .",
          "sudo mv mstar.logrotate-mailspool.te.txt mstar.logrotate-mailspool.te",
          "sudo dos2unix mstar.logrotate-mailspool.te",
          "sudo /usr/bin/make -f /usr/share/selinux/devel/Makefile mstar.logrotate-mailspool.pp",
          "sudo /usr/sbin/semodule -i mstar.logrotate-mailspool.pp",
          "sudo semanage fcontext -a -e /opt /data || true",
          "sudo semanage fcontext -a -t httpd_sys_content_t '/opt/www(/.*)?' || true",
          "sudo semanage fcontext -a -t httpd_log_t '/opt/apache_log(/.*)?' || true",
          "sudo semanage fcontext -a  -t mysqld_db_t '/opt/mysql_data(-files|-keyring)?(/.*)?' || true"
        ]
      }
    }
  ]
}

