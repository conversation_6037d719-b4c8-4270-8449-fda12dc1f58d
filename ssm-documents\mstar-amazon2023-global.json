﻿{
  "schemaVersion": "2.2",
  "description": "Amazon linux 2 hardening",
  "parameters": {
    "InitialWebText": {
      "type": "String",
      "description": "Initial message",
      "default": "Welcome to instance"
    }
  },
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "hardeningamazon2os",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "runCommand": [
          "sudo echo 'Converting mstar-global-linux recipe'",
          "sudo cp /tmp/s3_files/RPM-GPG-KEY-mstar.txt /etc/pki/rpm-gpg/RPM-GPG-KEY-mstar",
          "sudo chmod 644 /etc/pki/rpm-gpg/RPM-GPG-KEY-mstar",
          "cmd=`rpm -q gpg-pubkey-bdfd425b-54f4cb1e > /dev/null 2>&1;echo $?`; if [[ $cmd = 1 ]]; then sudo /bin/rpm --import /etc/pki/rpm-gpg/RPM-GPG-KEY-mstar; fi",
          "cmd=`/bin/rpm -q kernel > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo /bin/rpm --rebuilddb ; fi",
          "sudo cp /tmp/s3_files/Morningstar-Root-SHA2-intermediate.2026.pem /etc/pki/ca-trust/source/anchors/Morningstar-Root-SHA2-intermediate.2026.pem",
          "sudo cp /tmp/s3_files/Morningstar-Root-SHA2.2036.pem /etc/pki/ca-trust/source/anchors/Morningstar-Root-SHA2.2036.pem",
          "sudo /usr/bin/env update-ca-trust enable",
          "sudo /usr/bin/env update-ca-trust extract",
          "sudo echo 'HISTTIMEFORMAT=\"%d/%m/%y %T \"' | sudo tee /etc/profile.d/mstar.history.sh > /dev/null",
          "sudo cp /tmp/s3_files/logrotate_mailspool.txt /etc/logrotate.d/mailspool",
          "sudo cp /tmp/s3_files/ssh_config.txt /etc/ssh/ssh_config",
	  "sudo cp /tmp/s3_files/sshd_config.txt /etc/ssh/sshd_config",
          "sudo systemctl restart sshd.service",
	  "sudo cp /tmp/s3_files/cc_set_hostname.py /usr/lib/python3.9/site-packages/cloudinit/config/cc_set_hostname.py",
	  "sudo chmod 644 /usr/lib/python3.9/site-packages/cloudinit/config/cc_set_hostname.py",
	  "sudo cloud-init init",
	  "sudo cp /tmp/s3_files/99_mstarhostname_amzn.txt /etc/cloud/cloud.cfg.d/99_mstarhostname.cfg",
	  "sudo dos2unix /etc/cloud/cloud.cfg.d/99_mstarhostname.cfg"
        ]
      }
    }
  ]
}

