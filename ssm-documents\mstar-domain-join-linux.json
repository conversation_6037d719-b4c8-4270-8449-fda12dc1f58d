﻿{
  "schemaVersion": "2.2",
  "description": "Linux Domain Join",
  "parameters": {
    "InitialWebText": {
      "type": "String",
      "description": "Initial message",
      "default": "Welcome to instance"
    }
  },
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "domainjoin",
      "inputs": {
        "runCommand": [
          "#!/bin/bash",
          "if [ -e /mstar-metadata/base.json ]; then echo \"metadata file exists.\"; else echo \"/mstar-metadata/base.json file does not exist\"; exit 0; fi",
          "cmd=`jq -jr .domain_join /mstar-metadata/base.json |grep true`  ; if [[ $? -ne 0  ]]; then echo \"mstar metadata says do not join domain.\";  exit 0 ; fi",
          "cmd=`hostname|grep lin-` ; if [[ $? -ne 0 ]]; then echo \"This instance hostname does not follow Morningstar format. Cannot join domain.\"; exit 0 ; fi",
          "yum install oddjob oddjob-mkhomedir sssd samba-common-tools jq realmd adcli nc dos2unix -y",
          "sudo aws s3 sync s3://mstar-cloudcm-prod-repos/Linux/amazon2/template_files/ /tmp/s3_files/",
          "directory=\"/home/<USER>"",
          "if [ ! -d \"$directory\" ]; then mkdir -p \"$directory\" && echo \"Directory '$directory' created.\"; else echo \"Directory '$directory' already exists.\" ;fi",
          "TID=`awk -F \",\" '{print $1}' /mstar-metadata/base.json |awk -F \":\" '{print $2}'|tr -d '\"'`  #Get TID",
          "ENV=`awk -F \",\" '{print $2}' /mstar-metadata/base.json |awk -F \":\" '{print $2}'|tr -d '\"'` #Get ENV",
          "token=$(curl -X PUT -H \"X-aws-ec2-metadata-token-ttl-seconds: 60\" http://***************/latest/api/token)",
          "EC2_AVAIL_ZONE=$(curl -H \"X-aws-ec2-metadata-token: $token\" http://***************/latest/meta-data/placement/availability-zone)",
          "REGION=`echo $EC2_AVAIL_ZONE | sed 's/[a-z]$//'`",
          "cmd=`realm list |grep mstarext` ; if [[ $? -eq 0 ]]; then echo \"This instance is already joined to mstarext domain.\";  exit 1 ; fi",
          "DCPW=`aws --region=$REGION secretsmanager get-secret-value --secret-id=arn:aws:secretsmanager:$REGION:903581193685:secret:ssm_mstar_automation_key-OxC4Wp --output json | jq -r .SecretString|jq -r .domain_join_password`",
          "USEAST1=msprddcawse01; USWEST2=msprddcawsw01; EUCENTRAL1=msprddcawseuf01; EUWEST1=msprddcawsew01; APSOUTH1=msprddcawsaps01; APSOUTHEAST1=msprddcawsasi01; APSOUTHEAST2=msprddcawsase01; APSOUTHEAST4=msprddcawsapse1; APEAST1=msprddcawsape01",
          "USEAST1_2=msprddcawse02; USWEST2_2=msprddcawsw02; EUCENTRAL1_2=msprddcawseuf02; EUWEST1_2=msprddcawsew02; APSOUTH1_2=msprddcawsaps02; APSOUTHEAST1_2=msprddcawsasi02; APSOUTHEAST2_2=msprddcawsase02; APSOUTHEAST4_2=msprddcawsapse2; APEAST1_2=msprddcawsape02",
          "if [[ $REGION = us-east-1 ]]; then nc -zv $USEAST1.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-us-east-1",
          "elif [[ $REGION = us-west-2 ]]; then nc -zv $USWEST2.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-us-west-2",
          "elif [[ $REGION = eu-west-1 ]]; then nc -zv $EUWEST1.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-eu-west-1",
          "elif [[ $REGION = eu-central-1 ]]; then nc -zv $EUCENTRAL1.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-eu-central-1",
          "elif [[ $REGION = ap-south-1 ]]; then nc -zv $APSOUTH1.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-ap-south-1",
          "elif [[ $REGION = ap-east-1 ]]; then nc -zv $APEAST1.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-ap-east-1",
          "elif [[ $REGION = ap-southeast-1 ]]; then nc -zv $APSOUTHEAST1.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-ap-southeast-1",
          "elif [[ $REGION = ap-southeast-2 ]]; then nc -zv $APSOUTHEAST2.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-ap-southeast-2",
          "elif [[ $REGION = ap-southeast-4 ]]; then nc -zv $APSOUTHEAST4.mstarext.com 389  2>&1 |grep Connect > /tmp/nc-ap-southeast-4",
          "fi",
          "cmd=`cat /tmp/nc-$REGION|grep Connect` ; if [[ $? -ne 0 ]]; then echo \"Connectivity to mstarext DC failed. Kindly ensure to Attach private_active_directory SG to the instance\"; exit 1; fi",
          "if [[ $REGION = us-east-1 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$USEAST1_2.mstarext.com -vvv",
          "elif [[ $REGION = us-west-2 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$USWEST2.mstarext.com -vvv",
          "elif [[ $REGION = eu-central-1 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$EUCENTRAL1.mstarext.com -vvv",
          "elif [[ $REGION = eu-west-1 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$EUWEST1.mstarext.com -vvv",
          "elif [[ $REGION = ap-south-1 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$APSOUTH1.mstarext.com -vvv",
          "elif [[ $REGION = ap-southeast-1 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$APSOUTHEAST1.mstarext.com -vvv",
          "elif [[ $REGION = ap-southeast-2 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$APSOUTHEAST2.mstarext.com -vvv",
          "elif [[ $REGION = ap-southeast-4 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$APSOUTHEAST4.mstarext.com -vvv",
          "elif [[ $REGION = ap-east-1 ]]; then echo -n \"$DCPW\" | adcli join -U <EMAIL> --stdin-password  -O  \"OU=Servers,OU=$TID,OU=Servers and Services,DC=MSTAREXT,DC=COM\" -D MSTAREXT.COM --domain-controller=$APEAST1.mstarext.com -vvv",
          "fi",
          "cmd=`ls /tmp|grep krb5.conf` ; if [[ $? -eq 0 ]]; then echo \"krb5.conf template is already present in /tmp\"; else sudo cp /tmp/s3_files/krb5.conf /tmp ; fi",
          "cmd=`cat /etc/krb5.conf | grep mstarext`; if [[ $? -eq 0 ]]; then echo \"krb5 config is proper\"; else cat /tmp/krb5.conf > /etc/krb5.conf ; fi",
          "cmd=`ls /tmp|grep sssd.conf` ; if [[ $? -eq 0 ]]; then echo \"sssd.conf template is already present in /tmp\"; else sudo cp /tmp/s3_files/sssd.conf /tmp ; fi",
          "cmd=`cat /etc/sssd/sssd.conf | grep MSTAREXT` ; if [[ $? -eq 0 ]]; then echo \"sssd config is already correct\"; else cat /tmp/sssd.conf > /etc/sssd/sssd.conf && chmod 600 /etc/sssd/sssd.conf ; fi",
          "if [ $REGION = us-east-1 ]; then sed -i \"s/example01/$USEAST1/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$USEAST1_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$USEAST1/g\" /etc/krb5.conf; sed -i \"s/example02/$USEAST1_2/g\" /etc/krb5.conf",
          "elif [ $REGION = us-west-2 ]; then sed -i \"s/example01/$USWEST2/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$USWEST2_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$USWEST2/g\" /etc/krb5.conf; sed -i \"s/example02/$USWEST2_2/g\" /etc/krb5.conf",
          "elif  [ $REGION = eu-central-1 ]; then sed -i \"s/example01/$EUCENTRAL1/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$EUCENTRAL1_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$EUCENTRAL1/g\" /etc/krb5.conf; sed -i \"s/example02/$EUCENTRAL1_2/g\" /etc/krb5.conf",
          "elif [ $REGION = eu-west-1 ]; then sed -i \"s/example01/$EUWEST1/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$EUWEST1_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$EUWEST1/g\" /etc/krb5.conf; sed -i \"s/example02/$EUWEST1_2/g\" /etc/krb5.conf",
          "elif [ $REGION = ap-south-1 ]; then sed -i \"s/example01/$APSOUTH1/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$APSOUTH1_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$APSOUTH1/g\" /etc/krb5.conf; sed -i \"s/example02/$APSOUTH1_2/g\" /etc/krb5.conf",
          "elif [ $REGION = ap-southeast-1 ]; then sed -i \"s/example01/$APSOUTHEAST1/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$APSOUTHEAST1_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$APSOUTHEAST1/g\" /etc/krb5.conf; sed -i \"s/example02/$APSOUTHEAST1_2/g\" /etc/krb5.conf",
          "elif [ $REGION = ap-southeast-2 ]; then sed -i \"s/example01/$APSOUTHEAST2/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$APSOUTHEAST2_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$APSOUTHEAST2/g\" /etc/krb5.conf; sed -i \"s/example02/$APSOUTHEAST2_2/g\" /etc/krb5.conf",
          "elif [ $REGION = ap-southeast-4 ]; then sed -i \"s/example01/$APSOUTHEAST4/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$APSOUTHEAST4_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$APSOUTHEAST4/g\" /etc/krb5.conf; sed -i \"s/example02/$APSOUTHEAST4_2/g\" /etc/krb5.conf",
          "elif [ $REGION = ap-east-1 ]; then sed -i \"s/example01/$APEAST1/g\" /etc/sssd/sssd.conf; sed -i \"s/example02/$APEAST1_2/g\" /etc/sssd/sssd.conf; sed -i \"s/example01/$APEAST1/g\" /etc/krb5.conf; sed -i \"s/example02/$APEAST1_2/g\" /etc/krb5.conf",
          "fi",
          "echo \"changing the sssd group names\"",
          "sed -i \"s/example/$TID/g\" /etc/sssd/sssd.conf",
          "sed -i \"s/environment/$ENV/g\" /etc/sssd/sssd.conf",
          "authconfig --update --enablesssd --enablesssdauth --enablemkhomedir --disablefingerprint",
          "systemctl restart sssd",
          "realm list",
          "echo \"Adding appropriate AD groups in sudoers\"",
          "cmd=`ls /etc/sudoers.d|grep ms-admin-groups` ; if [[ $? -eq 0 ]]; then echo \"admins sudoers file template is already present in /etc/sudoers.d\"; else sudo cp /tmp/s3_files/ms-admin-groups /etc/sudoers.d ; fi",
          "cmd=`ls /etc/sudoers.d|grep ms-server-operations` ; if [[ $? -eq 0 ]]; then echo \"operations admins sudoers file template is already present in /etc/sudoers.d\"; else sudo cp /tmp/s3_files/ms-server-operations /etc/sudoers.d ; fi",
          "cmd=`ls /etc/sudoers.d|grep ms-servers-all-autodeploy` ; if [[ $? -eq 0 ]]; then echo \"autodeploy admins file template is already present in /etc/sudoers.d\"; else sudo cp /tmp/s3_files/ms-servers-all-autodeploy /etc/sudoers.d ; fi",
          "sed -i \"s/TID/$TID/g\" /etc/sudoers.d/ms-server-operations",
          "sed -i \"s/ENV/$ENV/g\" /etc/sudoers.d/ms-server-operations",
          "dos2unix /etc/sudoers.d/ms* /etc/sssd/sssd.conf /etc/krb5.conf"
        ]
      }
    }
  ]
}

