﻿{
  "schemaVersion": "2.2",
  "description": "Amazon linux 2 hardening",
  "parameters": {
    "InitialWebText": {
      "type": "String",
      "description": "Initial message",
      "default": "Welcome to instance"
    }
  },
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "hardeningamazon2os",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "runCommand": [
          "sudo yum install amazon-cloudwatch-agent -y",
          "sudo cp /tmp/s3_files/linux_config.txt /opt/aws/amazon-cloudwatch-agent/etc/linux_config.json",
          "sudo dos2unix /opt/aws/amazon-cloudwatch-agent/etc/linux_config.json",
          "sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/linux_config.json -s",
          "sudo rm -rf /tmp/s3_files/",
          "systemctl restart amazon-ssm-agent.service"
        ]
      }
    }
  ]
}

