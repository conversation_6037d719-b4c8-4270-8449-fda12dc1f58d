﻿{
  "schemaVersion": "2.2",
  "description": "Amazon linux 2 hardening",
  "parameters": {
    "InitialWebText": {
      "type": "String",
      "description": "Initial message",
      "default": "Welcome to instance"
    }
  },
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "hardeningamazon2os",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "runCommand": [
          "sudo systemctl restart amazon-ssm-agent.service",
          "#!/bin/bash",
          "token=$(curl -X PUT -H \"X-aws-ec2-metadata-token-ttl-seconds: 300\" http://***************/latest/api/token)",
	  "curl -H \"X-aws-ec2-metadata-token: $token\" http://***************/latest/user-data | grep 'Not Found'",
          "if [[ $? = 1 ]];",
          "then",
          "echo 'User Data exist'",
          "sleep 20",
          "fi",
          "max_iteration=20",
          "for i in $(seq 1 $max_iteration)",
          "do",
          "x=`ps -ef | grep yum | awk {'print $4'} | head -1`",
          "if [[ $x != 0 ]];",
          "then",
          "sleep 10",
          "echo 'Active YUM command exist'",
          "else",
          "echo 'NO Active YUM COMMAND'",
          "sudo yum install -y chrony adcli bc cifs-utils ethtool git krb5-workstation mailx perf  postfix samba-client screen sssd-ad sysstat tcpdump vim-enhanced wget ruby zip unzip oddjob dbus rpcbind cloud-init selinux-policy-devel selinux-policy-targeted atop nmap-ncat realmd trace-cmd dos2unix jq authconfig setools-console htop rtkit rsyslog crontabs",
          "break",
          "fi",
          "done",		
	  "releasever=`cat /etc/motd | grep \"Version\" | awk {'print $2'} | cut -d ':' -f 1`",
	  "sudo dnf upgrade --releasever=$releasever",
	  "cmd=`aws --version > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo rm -rf aws && sudo yum remove awscli -y && sudo curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o '/usr/bin/awscliv2.zip' && sudo unzip /usr/bin/awscliv2.zip -d /usr/bin && sudo /usr/bin/aws/install -i /usr/bin/aws-cli -b /usr/bin && sudo rm /usr/bin/awscliv2.zip -f && sudo rm -rf /usr/bin/aws/ && sudo ln -s /usr/bin/aws-cli/v2/current/bin/aws /usr/bin/aws; fi",
          "cmd=`ls /tmp/s3_files > /dev/null 2>&1;echo $?`; if [[ $cmd >=1 ]]; then mkdir /tmp/s3_files; fi",
          "sudo aws s3 sync s3://mstar-cloudcm-prod-repos/Linux/amazon2/template_files/ /tmp/s3_files/",
	  "sudo touch /etc/chrony.d/mstar.conf",
          "sudo cp /tmp/s3_files/chrony_server.conf.txt /etc/chrony.d/server.conf",
          "/usr/bin/dos2unix /etc/chrony.d/server.conf > /dev/null 2>&1",
          "sudo systemctl restart chronyd",
          "sudo systemctl enable --now chronyd",
          "sudo echo platform_family_rhel recipe",
          "sudo cp /tmp/s3_files/cloud.cfg /etc/cloud/cloud.cfg",
          "sudo chmod 644 /etc/cloud/cloud.cfg",
          "cmd=`sudo ls /data > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo mkdir /data; fi",
          "sudo /usr/bin/env restorecon -Rv /data",
          "sudo chown root:root /var/log/audit",
          "sudo mkdir -p /root/files/codedeploy",
          "sudo cp /tmp/s3_files/cc_set_hostname.py /usr/lib/python3.9/site-packages/cloudinit/config/cc_set_hostname.py",
          "sudo chmod 755 /etc/sysctl.d",
          "sudo cp /tmp/s3_files/ssh_banner.txt /etc/banner",
          "cmd=`sudo ls /etc/logrotate.d/bootlog > /dev/null 2>&1;echo $?`; if [[ $cmd -eq 0 ]]; then sudo rm /etc/logrotate.d/bootlog; fi",
          "sudo cp /tmp/s3_files/qualys.sh /etc/profile.d/qualys.sh",
          "sudo dos2unix /etc/profile.d/qualys.sh > /dev/null 2>&1",
          "sudo cp /tmp/s3_files/mstar.path.sh /etc/profile.d/mstar.path.sh",
          "sudo dos2unix /etc/profile.d/mstar.path.sh > /dev/null 2>&1",
          "sudo cp /tmp/s3_files/ec2-metadata.txt /usr/local/bin/ec2-metadata",
          "sudo cp /tmp/s3_files/ntp.conf /etc/ntp.conf",
          "cmd=`semanage fcontext -l | egrep '/data = /opt'; echo $?`; if [[ $cmd = 1 ]]; then semanage fcontext -a -e /opt /data; fi",
          "sudo cp /tmp/s3_files/atop.txt /etc/cron.daily/atop",
          "sudo chmod 700 /etc/cron.daily/atop",
          "sudo systemctl start atop; sudo systemctl enable atop",
          "sudo cp /tmp/s3_files/auditd.conf-rhel9.txt /etc/audit/auditd.conf",
          "sudo dos2unix /etc/audit/auditd.conf > /dev/null 2>&1",
          "sudo systemctl stop polkit",
          "cmd=`/usr/bin/timedatectl | egrep 'Local time:.*UTC|Time zone: UTC' 1> /dev/null; echo $?`; if [[ $cmd = 1 ]]; then systemctl stop polkit; timedatectl set-local-rtc 0; timedatectl set-timezone UTC; localectl set-locale LANG=en_US.UTF-8; systemctl start polkit; fi",
          "sudo echo 's3fs#mstar-prod-repos /s3/mstar-prod-repos fuse _netdev,allow_other,iam_role=auto,mp_umask=077 0 0' > /etc/fstab.s3fs",
          "sudo cp /tmp/s3_files/install /root/files/codedeploy/install",
          "cmd=`sudo test -f /etc/init.d/codedeploy-agent; echo $?`; if [ $cmd = 1 ]; then sudo chmod 700 /root/files/codedeploy/install; fi",
          "sudo /root/files/codedeploy/install auto",
          "sudo systemctl stop codedeploy-agent; sudo systemctl disable codedeploy-agent",
          "sudo echo 'add_drivers+=' ena '' > /etc/dracut.conf.d/ena.conf",
          "sudo chmod 644 /etc/dracut.conf.d/ena.conf",
          "sudo systemctl status rpcbind.socket; sudo systemctl enable rpcbind.socket",
          "sudo systemctl stop rpcbind 2> /dev/null; sudo systemctl disable rpcbind",
          "sudo echo 'vm.swappiness = 10' > /etc/sysctl.d/90-vm.swapiness.conf",
          "cmd=`grep vm.swappiness /etc/sysctl.d/90-vm.swapiness.conf > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then /bin/env sysctl -p /etc/sysctl.d/90-vm.swapiness.conf; fi",
          "sudo systemctl restart amazon-ssm-agent.service",
          "cmd=`sudo /sbin/sysctl -a | grep 'net.ipv6.conf.all.disable_ipv6 = 1' > /dev/null 2>&1 ; echo $?`; if [[ $cmd -ge 1 ]]; then sudo /sbin/sysctl -w net.ipv6.conf.all.disable_ipv6=1; fi",
          "sudo cp /tmp/s3_files/procps.txt /etc/rc.d/init.d/procps",
          "sudo sed -i '70i Defaults    logfile = \"/var/log/sudo.log\"' /etc/sudoers",
          "sudo sed -i 's/#Compress/Compress/g' /etc/systemd/journald.conf",
          "sudo sed -i 's/#ForwardToSyslog=no/ForwardToSyslog=yes/g' /etc/systemd/journald.conf",
          "cmd=`sudo cat /etc/systemd/journald.conf | grep persistent > /dev/null 2>&1;echo $?`; if [ $cmd == 1 ]; then sudo sed -i '$a\\Storage=persistent' /etc/systemd/journald.conf;fi",
          "sudo systemctl restart systemd-journald.service",
          "sudo sed -i 's/gpgcheck=True/gpgcheck=1/' /etc/dnf/dnf.conf",
          "sudo find /etc/yum.repos.d/ -name \"*.repo\" -exec echo \"Checking:\" {} \\; -exec sed -i 's/^gpgcheck\\s*=\\s*.*/gpgcheck=1/' {} \\;",
          "sudo sed -i 's/#ProcessSizeMax=2G/ProcessSizeMax=0/g' /etc/systemd/coredump.conf",
          "cmd=`sudo cat /etc/sudoers | grep use_pty > /dev/null 2>&1;echo $?`; if [ $cmd == 1 ]; then sudo sed -i '/env_reset$/a Defaults    use_pty' /etc/sudoers; fi",
          "cmd=`sudo cat /etc/security/faillock.conf | grep 'deny = 5' > /dev/null 2>&1;echo $?`; if [ $cmd == 1 ]; then sudo sed -i '/# deny/a deny = 5' /etc/security/faillock.conf; fi",
          "cmd=`sudo cat /etc/security/faillock.conf | grep -v 'root' | grep 'unlock_time = 900' > /dev/null 2>&1;echo $?`; if [ $cmd == 1 ]; then sudo sed -i '/# unlock_time/a unlock_time = 900' /etc/security/faillock.conf; fi"
        ]
      }
    }
  ]
}

