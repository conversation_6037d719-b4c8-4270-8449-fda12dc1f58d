﻿{
  "schemaVersion": "2.2",
  "description": "Amazon linux 2 hardening",
  "parameters": {
    "InitialWebText": {
      "type": "String",
      "description": "Initial message",
      "default": "Welcome to instance"
    }
  },
  "mainSteps": [
    {
      "action": "aws:runShellScript",
      "name": "hardeningamazon2os",
      "precondition": {
        "StringEquals": [
          "platformType",
          "Linux"
        ]
      },
      "inputs": {
        "runCommand": [
          "if [ -e /mstar-metadata/base.json ]; then cmd=`cat /mstar-metadata/base.json | grep '\"splunk\":\"ignore\"'  > /dev/null 2>&1; echo $?`; if [[ $cmd -eq 0 ]]; then echo 'Splunk Exception present';exit 0; fi; fi",
          "cmd=`egrep '^splunk' /etc/group > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo lgroupadd  -r -g 6052 splunk; fi",
          "cmd=`egrep '^splunk' /etc/passwd > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo luseradd -r -u 6052 -g 6052 -d /opt/splunkforwarder -s /bin/bash --gecos 'Splunk Server' splunk; fi",
          "cmd=`sudo ls -lrth /opt/splunkforwarder > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo mkdir /opt/splunkforwarder; sudo chmod 750 /opt/splunkforwarder; fi",
          "cmd=`uname -i | grep x86_64 > /dev/null 2>&1; echo $?` ; if [[ $cmd -eq 0 ]]; then sudo wget -O /tmp/splunkforwarder.rpm https://mstar-prod-repos.s3.amazonaws.com/linux/mstar-repos/redhat/7/external/x86_64/splunk.com/splunkforwarder-8.2.12-e973afd6886e.x86_64.rpm && yum localinstall /tmp/splunkforwarder.rpm -y; else sudo wget -O /opt/splunkforwarder-8.2.12-e973afd6886e-Linux-armv8.tgz \"https://mstar-prod-repos.s3.us-east-1.amazonaws.com/linux/mstar-repos/redhat/7/external/arm64/splunk.com/splunkforwarder-8.2.12-e973afd6886e-Linux-armv8.tgz\"; sudo tar -xvf /opt/splunkforwarder-8.2.12-e973afd6886e-Linux-armv8.tgz -C /opt/; fi",
          "sudo cp /dev/null /opt/splunkforwarder/etc/auth/splunk.secret",
          "sudo chmod 400 /opt/splunkforwarder/etc/auth/splunk.secret",
          "token=$(curl -X PUT -H \"X-aws-ec2-metadata-token-ttl-seconds: 60\" http://***************/latest/api/token)",
          "EC2_AVAIL_ZONE=$(curl -H \"X-aws-ec2-metadata-token: $token\" http://***************/latest/meta-data/placement/availability-zone)",
          "EC2_REGION=`echo $EC2_AVAIL_ZONE | sed 's/[a-z]$//'`",
          "sudo aws secretsmanager get-secret-value --secret-id=arn:aws:secretsmanager:$EC2_REGION:903581193685:secret:ssm_mstar_automation_key-OxC4Wp  > splunk.json",
          "sudo yum install -y jq",
          "sudo sed -i \"s/[\\]//g\" splunk.json; sudo sed -i \"s/}\\\"/}/\" splunk.json; sudo sed -i \"s/\\\"{/{/\" splunk.json",
          "sudo jq '.SecretString .indexer_secret' splunk.json | tr -d '\"' | sudo tee -a /opt/splunkforwarder/etc/auth/splunk.secret",
          "sudo cp /dev/null /opt/splunkforwarder/etc/passwd",
          "echo \":admin:password\" | sudo tee /opt/splunkforwarder/etc/passwd > /dev/null",
          "sudo chmod 600 /opt/splunkforwarder/etc/passwdawscliv2.zip",
          "sudo cp /tmp/s3_files/deploymentclient.conf.my_app_name.sh /opt/splunkforwarder/bin/deploymentclient.conf.my_app_name.sh",
          "sudo dos2unix /opt/splunkforwarder/bin/deploymentclient.conf.my_app_name.sh",
          "sudo cloud-init init",
          "cmd=`sudo ls /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo mkdir /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient; fi",
          "sudo chmod 755 /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient",
          "cmd=`sudo ls /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo mkdir /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default; fi",
          "sudo chmod 755 /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default",
          "sudo cp /tmp/s3_files/server.conf-splunkforwarder /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default/server.conf",
          "sudo dos2unix /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default/server.conf",
          "sudo echo -e \"[deployment-client]\n  phoneHomeIntervalInSecs = 60\n\n[target-broker:deploymentServer]\n  targetUri = splunk-ds-region_var.inf01ec5.eas.morningstar.com:8089\" | sudo tee /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default/deploymentclient.conf",
          "sudo sed -i \"s/region_var/$EC2_REGION/g\" /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default/deploymentclient.conf",
          "x=`jq '.SecretString .pass4SymmKey' splunk.json | tr -d '\"'`; sudo sed -i \"s/pass4symmkey/$x/\" /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default/server.conf",
          "y=`jq '.SecretString .sslPassword' splunk.json | tr -d '\"'`; sudo sed -i \"s/sslpassword/$y/\" /opt/splunkforwarder/etc/apps/app_splunk_deploymentclient/default/server.conf",
          "z=`jq '.SecretString .admin_password' splunk.json | tr -d '\"' | sed -r 's|/|\\\\/|g'`; echo $z; sudo sed -i \"s|password|$z|\" /opt/splunkforwarder/etc/passwd",
          "sudo cat /opt/splunkforwarder/etc/passwd",
          "sudo echo -e \"[deployment-client]\n  clientName = CHANGE_ME\" | sudo tee /opt/splunkforwarder/etc/system/local/deploymentclient.conf > /dev/null",
          "cmd=`sudo ls /mstar-metadata/base.json > /dev/null 2>&1; echo $?`; if [[ $cmd -eq 0 ]]; then account_id_alias=`sudo jq '.\"tid\"' /mstar-metadata/base.json | tr -d '\"'`;sudo sed -i \"s@CHANGE_ME@aws_${EC2_REGION}_${account_id_alias}-default@g\" /opt/splunkforwarder/etc/system/local/deploymentclient.conf; fi",
          "account_id=$(curl -s -H \"X-aws-ec2-metadata-token: $token\" http://***************/latest/dynamic/instance-identity/document | jq -r .accountId)",
          "instance_id=$(curl -s -H \"X-aws-ec2-metadata-token: $token\" http://***************/latest/meta-data/instance-id)",
          "echo -e \"[default]\nhost = ${account_id}_${instance_id}\" | sudo tee /opt/splunkforwarder/etc/system/local/inputs.conf",
          "cmd=`sudo ls /data/app_logs > /dev/null 2>&1; echo $?`; if [[ $cmd -ge 1 ]]; then sudo mkdir /data/app_logs ; fi",
          "sudo cp /tmp/s3_files/README.app_logs /data/app_logs/README",
          "sudo dos2unix /data/app_logs/README",
          "sudo /opt/splunkforwarder/bin/splunk enable boot-start -user splunk --accept-license",
          "sudo /opt/splunkforwarder/bin/splunk clone-prep-clear-config",
          "sudo chown -R splunk:splunk /opt/splunkforwarder",
          "sudo systemctl daemon-reload",
          "sudo systemctl start splunk",
          "sudo /opt/splunkforwarder/bin/splunk enable boot-start -user splunk --accept-license",
          "sudo rm -vf /opt/splunkforwarder/cloneprep",
          "sudo rm -rvf /opt/splunkforwarder/var/log/splunk/*",
          "sudo rm -rvf /opt/splunkforwarder/var/log/introspection/*",
          "sudo /opt/splunkforwarder/bin/splunk clone-prep-clear-config",
          "sudo systemctl daemon-reload",
          "sudo systemctl start splunk",
          "sudo rm -rvf /opt/splunkforwarder/var/log/splunk/*",
          "sudo rm -rvf /opt/splunkforwarder/var/log/introspection/*",
          "sudo rm -f splunk.json"
        ]
      }
    }
  ]
}

