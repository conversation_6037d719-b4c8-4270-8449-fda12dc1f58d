<#
.SYNOPSIS
  Recursively download SSM documents (and any nested documents they reference)
.PARAMETER DocumentArn
  The ARN or name of the root SSM document to fetch.
.PARAMETER Region
  AWS region (default: us-east-1)
.PARAMETER OutputFolder
  Local folder to save documents (default: .\ssm-documents)
.EXAMPLE
  .\Get-SSMDocuments.ps1 -DocumentArn arn:aws:ssm:us-east-1:903581193685:document/mstar-base-amazon2023
#>

param(
    [Parameter(Mandatory)]
    [string]$DocumentArn,
    [string]$Region       = 'us-east-1',
    [string]$OutputFolder = '.\ssm-documents'
)

# ensure output folder exists
if (!(Test-Path $OutputFolder)) {
    New-Item -ItemType Directory -Path $OutputFolder | Out-Null
}

# track which docs we've already downloaded
$visited = @{}

function Download-SSMDocument {
    param([string]$Name)

    if ($visited.ContainsKey($Name)) {
        return
    }
    $visited[$Name] = $true

    Write-Host "▶ Downloading SSM Document: $Name" -ForegroundColor Cyan

    # fetch the document content (JSON string)
    $content = aws ssm get-document `
        --name $Name `
        --region $Region `
        --query Content `
        --output text

    if (-not $content) {
        Write-Warning "No content returned for document $Name"
        return
    }

    # save to file
    # derive a safe file name from just the document name (strip ARN path if needed)
    $simpleName = if ($Name -match '/document/(.+)$') { $Matches[1] } else { $Name }
    # For ARN format, extract just the document name after the last slash
    if ($simpleName -match '^arn:aws:ssm:') {
        $simpleName = $simpleName -replace '^.*/', ''
    }
    # Sanitize filename by removing invalid characters
    $simpleName = $simpleName -replace '[<>:"/\\|?*]', '_'
    $filePath = Join-Path $OutputFolder "$simpleName.json"

    # Use Set-Content instead of Out-File to avoid path issues
    try {
        Set-Content -Path $filePath -Value $content -Encoding UTF8
    }
    catch {
        Write-Warning "Failed to save file $filePath : $($_.Exception.Message)"
        return
    }

    # try parsing JSON to find nested DocumentName and documentPath properties
    try {
        $json = $content | ConvertFrom-Json -ErrorAction Stop

        # Extract documentPath values from mainSteps
        $documentPaths = @()
        if ($json.mainSteps) {
            $documentPaths = $json.mainSteps | Where-Object { $_.inputs.documentPath } | ForEach-Object { $_.inputs.documentPath }
        }

        # Also look for DocumentName properties (for other document types)
        function Get-DocumentNames($obj) {
            $out = @()
            if ($null -eq $obj) { return $out }
            if ($obj -is [System.Collections.IDictionary]) {
                foreach ($k in $obj.Keys) {
                    if ($k -eq 'DocumentName' -and $obj[$k]) {
                        $out += $obj[$k]
                    }
                    else {
                        $out += Get-DocumentNames $obj[$k]
                    }
                }
            }
            elseif ($obj -is [System.Collections.IEnumerable] -and -not ($obj -is [string])) {
                foreach ($item in $obj) {
                    $out += Get-DocumentNames $item
                }
            }
            return $out
        }

        $documentNames = Get-DocumentNames $json
        $nested = ($documentPaths + $documentNames) | Where-Object { $_ } | Select-Object -Unique
    }
    catch {
        # fallback: simple regex scan for both DocumentName and documentPath
        $documentNames = [regex]::Matches($content, '"DocumentName"\s*:\s*"([^"]+)"') |
                        ForEach-Object { $_.Groups[1].Value }
        $documentPaths = [regex]::Matches($content, '"documentPath"\s*:\s*"([^"]+)"') |
                        ForEach-Object { $_.Groups[1].Value }
        $nested = ($documentNames + $documentPaths) | Select-Object -Unique
    }

    foreach ($child in $nested) {
        if ($child) {
            Download-SSMDocument -Name $child
        }
    }
}

# kick it off
Download-SSMDocument -Name $DocumentArn

Write-Host "✅ All done. Documents saved under $OutputFolder" -ForegroundColor Green
